"use client";
import React, { useCallback, useEffect, useState } from "react";
import ReactFlow, {
  Controls,
  Background,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  BackgroundVariant,
} from "reactflow";
import "reactflow/dist/style.css";

import OBSConnectForm from "../src/components/OBSConnectForm";
import SceneNode from "../src/components/SceneNode";
import TransitionEdge from "../src/components/TransitionEdge";
import TransitionEditorPanel from "../src/components/TransitionEditorPanel";
import { useOBS } from "../src/hooks/useOBS";

const nodeTypes = { sceneNode: SceneNode };
const edgeTypes = { transitionEdge: TransitionEdge };

export default function Home() {
  const { connectOBS, isConnected, scenes, error, transitionToScene } =
    useOBS();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [selectedEdge, setSelectedEdge] = useState<Edge | null>(null);

  useEffect(() => {
    console.log("Scenes in page.tsx useEffect:", scenes);
    if (scenes.length > 0) {
      const initialNodes = scenes.map((scene, index) => ({
        id: scene.sceneName,
        type: "sceneNode",
        position: { x: 250, y: 50 + index * 100 },
        data: { label: scene.sceneName },
      }));
      setNodes(initialNodes);
    }
  }, [scenes, setNodes]);

  const onConnect = useCallback(
    (params: Connection) => {
      setEdges((eds) =>
        addEdge(
          {
            ...params,
            type: "transitionEdge",
            data: { transitionToScene, transitionName: "Cut", duration: 300 },
          },
          eds
        )
      );
    },
    [setEdges, transitionToScene]
  );

  const onEdgeClick = useCallback((event: React.MouseEvent, edge: Edge) => {
    event.stopPropagation(); // Prevent ReactFlow from deselecting the edge
    setSelectedEdge(edge);
  }, []);

  const onUpdateEdge = useCallback(
    (edgeId: string, data: { transitionName?: string; duration?: number }) => {
      setEdges((eds) =>
        eds.map((edge) => {
          if (edge.id === edgeId) {
            return {
              ...edge,
              data: {
                ...edge.data,
                ...data,
              },
            };
          }
          return edge;
        })
      );
    },
    [setEdges]
  );

  const onCloseEditor = useCallback(() => {
    setSelectedEdge(null);
  }, []);

  return (
    <div className="flex flex-col h-screen">
      <div className="p-4 bg-gray-100 flex justify-between items-center">
        <h1 className="text-2xl font-bold">OBS Scene Transition Editor</h1>
        <OBSConnectForm onConnect={connectOBS} isConnected={isConnected} />
      </div>
      {error && (
        <div className="p-2 bg-red-200 text-red-800">Error: {error}</div>
      )}
      <div className="flex-grow relative">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onEdgeClick={onEdgeClick}
          nodeTypes={nodeTypes}
          edgeTypes={edgeTypes}
          fitView
        >
          <MiniMap />
          <Controls />
          <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
        </ReactFlow>
        <TransitionEditorPanel
          selectedEdge={selectedEdge}
          onClose={onCloseEditor}
          onUpdateEdge={onUpdateEdge}
        />
      </div>
    </div>
  );
}
