import React from "react";
import {
  BaseEdge,
  EdgeLabelRenderer,
  EdgeProps,
  getSimpleBezierPath,
} from "reactflow";

interface TransitionEdgeProps extends EdgeProps {
  data?: {
    transitionToScene: (
      sceneName: string,
      transitionName: string,
      duration: number
    ) => void;
    transitionName?: string;
    duration?: number;
  };
}

const TransitionEdge: React.FC<TransitionEdgeProps> = ({
  id,
  sourceX,
  sourceY,
  targetX,
  targetY,
  sourcePosition,
  targetPosition,
  style = {},
  markerEnd,
  data,
}) => {
  const [edgePath, labelX, labelY] = getSimpleBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  const handleClick = () => {
    if (data?.transitionToScene) {
      // Placeholder values for transitionName and duration
      data.transitionToScene(
        id.split("__")[1],
        data.transitionName || "Cut",
        data.duration || 300
      );
    }
  };

  return (
    <>
      <BaseEdge path={edgePath} markerEnd={markerEnd} style={style} />
      <EdgeLabelRenderer>
        <div
          style={{
            position: "absolute",
            transform: `translate(-50%, -50%) translate(${labelX}px,${labelY}px)`,
            fontSize: 12,
            pointerEvents: "all",
          }}
          className="nodrag nopan"
        >
          <button className="edgebutton" onClick={handleClick}>
            ▶
          </button>
        </div>
      </EdgeLabelRenderer>
    </>
  );
};

export default TransitionEdge;
