import React, { useState, useEffect } from "react";
import { Edge } from "reactflow";

interface TransitionEditorPanelProps {
  selectedEdge: Edge | null;
  onClose: () => void;
  onUpdateEdge: (
    edgeId: string,
    data: { transitionName?: string; duration?: number }
  ) => void;
}

const TransitionEditorPanel: React.FC<TransitionEditorPanelProps> = ({
  selectedEdge,
  onClose,
  onUpdateEdge,
}) => {
  const [transitionName, setTransitionName] = useState("");
  const [duration, setDuration] = useState(300);

  useEffect(() => {
    if (selectedEdge) {
      setTransitionName(selectedEdge.data?.transitionName || "Cut");
      setDuration(selectedEdge.data?.duration || 300);
    }
  }, [selectedEdge]);

  const handleSave = () => {
    if (selectedEdge) {
      onUpdateEdge(selectedEdge.id, { transitionName, duration });
      onClose();
    }
  };

  if (!selectedEdge) {
    return null;
  }

  return (
    <div className="absolute right-4 top-4 bg-white p-4 rounded shadow-lg z-10">
      <h2 className="text-lg font-bold mb-2">Edit Transition</h2>
      <div className="mb-2">
        <label
          htmlFor="transitionName"
          className="block text-sm font-medium text-gray-700"
        >
          Transition Name:
        </label>
        <input
          type="text"
          id="transitionName"
          value={transitionName}
          onChange={(e) => setTransitionName(e.target.value)}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
        />
      </div>
      <div className="mb-4">
        <label
          htmlFor="duration"
          className="block text-sm font-medium text-gray-700"
        >
          Duration (ms):
        </label>
        <input
          type="number"
          id="duration"
          value={duration}
          onChange={(e) => setDuration(Number(e.target.value))}
          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
        />
      </div>
      <div className="flex justify-end space-x-2">
        <button
          onClick={onClose}
          className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default TransitionEditorPanel;
