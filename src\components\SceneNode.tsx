import React, { memo } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";

interface SceneNodeProps {
  data: {
    label: string;
  };
}

const SceneNode: React.FC<SceneNodeProps> = ({ data }) => {
  return (
    <div className="px-4 py-2 shadow-md rounded-full bg-white border-2 border-stone-400">
      <div className="flex">
        <div className="text-lg font-bold">{data.label}</div>
      </div>
      <Handle
        type="target"
        position={Position.Top}
        className="w-16 !bg-teal-500"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-16 !bg-teal-500"
      />
    </div>
  );
};

export default memo(SceneNode);
