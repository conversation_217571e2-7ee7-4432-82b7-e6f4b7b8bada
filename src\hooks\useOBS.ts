import { useState, useEffect, useCallback } from "react";
import OBSWebSocket from "obs-websocket-js";

interface Scene {
  sceneIndex: number;
  sceneName: string;
}

export const useOBS = () => {
  const [obs] = useState(new OBSWebSocket());
  const [isConnected, setIsConnected] = useState(false);
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchScenes = useCallback(async () => {
    if (!isConnected) return;
    try {
      const response = await obs.call("GetSceneList");
      console.log("Raw response from GetSceneList:", response);

      // Transform the JsonObject[] to Scene[] by mapping the properties
      const transformedScenes: Scene[] = response.scenes.map(
        (scene: any, index: number) => ({
          sceneIndex: scene.sceneIndex ?? index,
          sceneName: scene.sceneName,
        })
      );

      setScenes(transformedScenes);
    } catch (error: any) {
      console.error("Failed to fetch scenes", error);
      setError(error.message);
    }
  }, [obs, isConnected]);

  const connectOBS = useCallback(
    async (ip: string, port: string, password?: string) => {
      try {
        const { obsWebSocketVersion, rpcVersion } = await obs.connect(
          `ws://${ip}:${port}`,
          password
        );
        console.log(
          `Connected to OBS ${obsWebSocketVersion} (RPC v${rpcVersion})`
        );
        setIsConnected(true);
        setError(null);
        fetchScenes(); // Call fetchScenes here after successful connection
      } catch (error: any) {
        console.error("Failed to connect to OBS", error);
        setError(error.message);
        setIsConnected(false);
      }
    },
    [obs, fetchScenes]
  ); // Add fetchScenes to dependency array

  const transitionToScene = useCallback(
    async (sceneName: string, transitionName: string, duration: number) => {
      if (!isConnected) return;
      try {
        // Check if SetCurrentSceneTransition is available, otherwise use SetCurrentProgramScene
        const { sceneName: currentSceneName } = await obs.call(
          "GetCurrentProgramScene"
        );
        if (currentSceneName !== sceneName) {
          await obs.call("SetCurrentSceneTransition", {
            transitionName: transitionName,
          });
          await obs.call("SetCurrentProgramScene", { sceneName: sceneName });
          console.log(
            `Transitioned to scene: ${sceneName} with transition: ${transitionName}`
          );
        } else {
          console.log(`Already in scene: ${sceneName}`);
        }
      } catch (error: any) {
        console.error("Failed to transition scene", error);
        setError(error.message);
      }
    },
    [obs, isConnected]
  );

  useEffect(() => {
    obs.on("ConnectionClosed", () => {
      console.log("OBS connection closed");
      setIsConnected(false);
    });
    obs.on("ConnectionError", (err) => {
      console.error("OBS connection error", err);
      setError(err.message);
      setIsConnected(false);
    });

    return () => {
      obs.off("ConnectionClosed");
      obs.off("ConnectionError");
    };
  }, [obs]);

  return {
    connectOBS,
    isConnected,
    scenes,
    error,
    transitionToScene,
  };
};
