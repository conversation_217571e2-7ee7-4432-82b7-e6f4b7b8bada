import { useState, useEffect, useCallback } from "react";
import OBSWebSocket from "obs-websocket-js";

interface Scene {
  sceneIndex: number;
  sceneName: string;
}

export const useOBS = () => {
  const [obs] = useState(new OBSWebSocket());
  const [isConnected, setIsConnected] = useState(false);
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [error, setError] = useState<string | null>(null);

  const fetchScenes = useCallback(async () => {
    console.log("fetchScenes called, isConnected:", isConnected);
    if (!isConnected) {
      console.log("Not connected, skipping fetchScenes");
      return;
    }
    try {
      console.log("Calling GetSceneList...");
      const response = await obs.call("GetSceneList");
      console.log("Raw response from GetSceneList:", response);
      console.log("Response.scenes:", response.scenes);
      console.log("Response.scenes length:", response.scenes?.length);

      // Transform the JsonObject[] to Scene[] by mapping the properties
      const transformedScenes: Scene[] = response.scenes.map(
        (scene: any, index: number) => {
          console.log("Processing scene:", scene);
          return {
            sceneIndex: scene.sceneIndex ?? index,
            sceneName: scene.sceneName,
          };
        }
      );

      console.log("Transformed scenes:", transformedScenes);
      setScenes(transformedScenes);
    } catch (error: any) {
      console.error("Failed to fetch scenes", error);
      setError(error.message);
    }
  }, [obs, isConnected]);

  const connectOBS = useCallback(
    async (ip: string, port: string, password?: string) => {
      console.log(`Attempting to connect to OBS at ws://${ip}:${port}`);
      try {
        const { obsWebSocketVersion, rpcVersion } = await obs.connect(
          `ws://${ip}:${port}`,
          password
        );
        console.log(
          `Connected to OBS ${obsWebSocketVersion} (RPC v${rpcVersion})`
        );
        setIsConnected(true);
        setError(null);
        console.log("Connection successful, calling fetchScenes...");
        fetchScenes(); // Call fetchScenes here after successful connection
      } catch (error: any) {
        console.error("Failed to connect to OBS", error);
        setError(error.message);
        setIsConnected(false);
      }
    },
    [obs, fetchScenes]
  ); // Add fetchScenes to dependency array

  const transitionToScene = useCallback(
    async (sceneName: string, transitionName: string, duration: number) => {
      if (!isConnected) return;
      try {
        // Check if SetCurrentSceneTransition is available, otherwise use SetCurrentProgramScene
        const { sceneName: currentSceneName } = await obs.call(
          "GetCurrentProgramScene"
        );
        if (currentSceneName !== sceneName) {
          await obs.call("SetCurrentSceneTransition", {
            transitionName: transitionName,
          });
          await obs.call("SetCurrentProgramScene", { sceneName: sceneName });
          console.log(
            `Transitioned to scene: ${sceneName} with transition: ${transitionName}`
          );
        } else {
          console.log(`Already in scene: ${sceneName}`);
        }
      } catch (error: any) {
        console.error("Failed to transition scene", error);
        setError(error.message);
      }
    },
    [obs, isConnected]
  );

  useEffect(() => {
    obs.on("ConnectionClosed", () => {
      console.log("OBS connection closed");
      setIsConnected(false);
    });
    obs.on("ConnectionError", (err) => {
      console.error("OBS connection error", err);
      setError(err.message);
      setIsConnected(false);
    });

    return () => {
      obs.off("ConnectionClosed");
      obs.off("ConnectionError");
    };
  }, [obs]);

  return {
    connectOBS,
    isConnected,
    scenes,
    error,
    transitionToScene,
  };
};
